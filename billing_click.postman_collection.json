{"info": {"_postman_id": "375b0b7f-5638-4318-8446-56b15050eeb1", "name": "billing_click", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "17469950", "_collection_link": "https://solar-eclipse-316690.postman.co/workspace/MyTaxi~07669c6b-3242-40f6-9e56-40c429820e23/collection/17469950-375b0b7f-5638-4318-8446-56b15050eeb1?action=share&source=collection_link&creator=17469950"}, "item": [{"name": "Создание и редактирование обьектов", "item": [{"name": "Создание пользователя", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>client_reg</action>\n<username>{{username}}</username>\n<password>{{password}}</password>\n<client_id>43043</client_id>\n<phone>998909562145</phone>\n<last_name>Nurjanone</last_name>\n<first_name>Client</first_name>\n<middle_name></middle_name>\n<date_of_birth>1986-05-05</date_of_birth>\n<city>Ташкент</city>\n<district>Яккасарайский</district>\n<street>Братислава</street>\n<house>5</house>\n<contract_number>crt51452</contract_number>\n<contract_date>2022-05-07</contract_date>\n<lang>RU</lang>\n<sms>0</sms>\n<corp>\n\t<corp_id>40</corp_id>\n</corp>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Редактирование пользователя", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>client_edit</action>\n<username>{{username}}</username>\n<password>{{password}}</password>\n<client_id>111</client_id>\n<phone>998909890889</phone>\n<last_name>Nurjan</last_name>\n<first_name>Clientone</first_name>\n<corp>\n\t<corp_id>40</corp_id>\n</corp>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Регистрация компании", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>corp_client_reg</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<corp_client_id>444442</corp_client_id>--идентификатор компании (число) - обязательно\n<name>test</name>--наименование\n<date_create_corp>2015-05-05</date_create_corp>--дата создания корп клиента\n<phone>998909562145</phone>--номер телефона (число)\n<inn>7777777</inn>--ИНН\n<acc>20208000124574512001</acc>--расчетный счет корп клиента\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Регистрация таксопарка", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>company_reg</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<company_id>130</company_id>--идентификатор компании в MyTaxi (число)- обязательно\n<name>Сититакси</name>--наименование\n<date_create_company>2015-05-05</date_create_company>--дата создания компании\n<inn>7777777</inn>--ИНН\n<acc>20208000124574512001</acc>--расчетный счет компании\n<city>Ташкент</city>\n<district>Яккасарайский</district>\n<street>Братислава</street>\n<house>5</house>\n<phone>998909562145</phone>--номер телефона (число)\n<contract_number>crt51452</contract_number>\n<contract_date>2015-05-07</contract_date>\n<lang>RU</lang>--значения RU или UZ\n<sms>1</sms>--значения 0 - не оповещать по смс, 1 - оповещать\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}]}, {"name": "Удаление/увольнение/блокировка", "item": [{"name": "Увольнение водителя", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_fire</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>130</driver_id>--идентификатор водителя в MyTaxi (число) - обязательно\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Блокировка аккаунта пользователя", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>client_fire</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>43043</client_id>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}]}, {"name": "Списание денег", "item": [{"name": "Списание денег с компании", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>corp_fine</action>\n<mytaxi_id>************</mytaxi_id>--уникальный ID операции платежей в майтакси (число)—обязательно (транзакция)\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<company_id>40</company_id>\n<amount>1000</amount> -- сумма для списания\n<comment>Причина списания</comment>\n--<id_account_to></id_account_to> -- счет в котором будет фиксироваться списанная сумма\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Списание денег с таксопарка", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>company_pay</action>\n<mytaxi_id>************</mytaxi_id>--уникальный ID операции платежей в майтакси (число)—обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<company_id>130</company_id>\n<amount>1000</amount>-- сумма для списания\n<comment>Причина списания</comment>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Списание средств с водителя", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_fine</action>\n<mytaxi_id>****************</mytaxi_id>--уникальный ID операции платежей в майтакси (число)—обязательно (транзакция)\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>111</driver_id>\n<amount>1000</amount> -- сумма для списания\n<comment>Причина списания</comment>\n<id_account_to>11583</id_account_to> -- счет в котором будет фиксироваться списанная сумма\n</ROOT>\n"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}}, "response": []}]}, {"name": "Partner", "item": [{"name": "Add Partner", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}", "type": "text"}, {"key": "APP-VERSION", "value": "12", "type": "text"}, {"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<ROOT>\n<action>partner_add</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<name>NewPartnerPart1</name>\n<inn>********</inn>\n<prcnt>2</prcnt>\n<partner_id>20000</partner_id>\n</ROOT>"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}, "description": "Добавление нового партнера"}, "response": []}, {"name": "Add Partner Deposit", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}", "type": "text"}, {"key": "APP-VERSION", "value": "12", "type": "text"}, {"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<ROOT>\n<mytaxi_id>*********</mytaxi_id>\n<action>partner_deposit</action>\n<username>MytaxiAPIClick2015</username>\n<password>6dd21114327bf55c16af905fe0acff845acce3e5fb39f72aab25a40a31bb355c</password>\n<amount>1000</amount>\n<description>test</description>\n<dtime>2025-01-14</dtime>\n<service>3</service>\n<cash>5</cash>\n<partner_id>204</partner_id>\n</ROOT>"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}, "description": "Добавление нового партнера"}, "response": []}, {"name": "Get Partner", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}", "type": "text"}, {"key": "APP-VERSION", "value": "12", "type": "text"}, {"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<ROOT>\n<action>get_partner</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<partner_id>268</partner_id>\n</ROOT>"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}, "description": "Добавление нового партнера"}, "response": []}, {"name": "Edit Partner", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": "<ROOT>\n<action>partner_edit</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<name>Test Partner 111</name>\n<inn>********9</inn>\n<prcnt>2</prcnt>\n<partner_id>1</partner_id>\n</ROOT>"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}, "description": "Редактирование данных партнера"}, "response": []}]}, {"name": "Driver", "item": [{"name": "Add Driver", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_reg</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>131</driver_id>--идентификатор водителя в MyTaxi (число) - обязательно\n<last_name>Jon</last_name>--фамилия\n<first_name>Snow</first_name>--имя\n<middle_name><PERSON><PERSON><PERSON></middle_name>--отчество\n<date_of_birth>2015-05-05</date_of_birth>\n<phone>998909562145</phone>--номер телефона (число)\n<contract_number>crt51452</contract_number>\n<contract_date>2015-05-07</contract_date>\n<lang>RU</lang>--значения RU или UZ\n<sms>1</sms>--значения 0 - не оповещать по смс, 1 - оповещать\n<company_id>6</company_id>--идентификатор компании в MyTaxi (число)\n<rate>15</rate>--процентная ставка(целое число)\n<cashless_rides>1</cashless_rides>--значения 0 – не принимать безнал заказы, 1 - принимать\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}, "description": "Регистрация нового водителя"}, "response": []}, {"name": "promo balance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_driver_promo_bal</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>111</driver_id>--идентификатор водителя в MyTaxi (число) - обязательно\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "promo balance to balance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_promoacc_acc</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>111</driver_id>--идентификатор водителя в MyTaxi (число) - обязательно\n<amount>1000</amount>\n<description></description>\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "promo balance to card", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_promoacc_pcard</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>111</driver_id>--идентификатор водителя в MyTaxi (число) - обязательно\n<amount>1000</amount>\n<description></description>\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "balance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_debt</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<amount>1000</amount>\n<mytaxi_id>112232332312312</mytaxi_id>\n<driver_id>111</driver_id>--идентификатор водителя в MyTaxi (число) - обязательно\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Баланс водителя", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}", "disabled": true}, {"key": "APP-VERSION", "value": "12", "disabled": true}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_acc_bal_driver</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>111</driver_id>\n</ROOT>"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}, "description": "Получение баланса водителя"}, "response": []}, {"name": "Получение истории о приходе и расходе средств водителя", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>history_driver</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver>10137</driver>\n<limit>40</limit>\n<skip>0</skip>\n</ROOT>\n"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}}, "response": []}, {"name": "activate driver", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_active</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>111</driver_id>\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "balance history", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_driver_balance_history</action>\n<username>{{username}}</username>\n<password>{{password}}</password>\n<driver_id>9649</driver_id>\n<limit>40</limit>\n<skip>0</skip>\n</ROOT>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://**************:88", "protocol": "http", "host": ["185", "100", "53", "100"], "port": "88"}}, "response": []}, {"name": "Получить историю пополнений водителей", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_acc_dep_driver</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>937</driver_id>\n<date_from>2016-08-19</date_from>\n<date_to>2017-12-19</date_to>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}]}, {"name": "TaxiPark", "item": [{"name": "Add TaxiPark", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>partners_park_add</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<company_id>130</company_id>-- ИД таксопарка\n<name></name>-- название таксопарка\n<date_create_company>2015-05-05</date_create_company>--дата создания таксопарка\n<inn>7777777</inn>--ИНН\n<acc>20208000124574512001</acc>-- расчетный счет таксопарка\n<phone>998909562145</phone>-- номер телефона (число)\n<percent>3</percent>\n<partner_id>1</partner_id>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Edit TaxiPark", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>company_edit</action>-- обязательно\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<company_id>130</company_id>-- ИД таксопарка\n<name>Сититакси</name>-- название таксопарка\n<date_create_company>2015-05-05</date_create_company>--дата создания таксопарка\n<inn>7777777</inn>--ИНН\n<acc>20208000124574512001</acc>-- расчетный счет таксопарка\n<phone>998909562145</phone>-- номер телефона (число)\n<percent>3</percent>\n<partner_id>1</partner_id>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}]}, {"name": "Проверка баланса", "item": [{"name": "Баланс компании", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_acc_bal_corp_client</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<corp_client_id>1</corp_client_id>\n</ROOT>"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}}, "response": []}, {"name": "Баланс таксопарка", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_acc_bal_company</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<company_id>711</company_id>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "partner balance", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_partner_balance</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<partner_id>1</partner_id>\n</ROOT>"}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}}, "response": []}, {"name": "Получить историю пополнений компании", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_acc_dep_corp</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<corp_client_id>40</corp_client_id>\n<date_from>2016-08-19</date_from>\n<date_to>2016-10-19</date_to>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "get client cashback", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_client_bonus</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>111</client_id>\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}]}, {"name": "Перевод денег с водителя в ТС", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>transfer_driver_ts</action>\n<mytaxi_id>124578455559</mytaxi_id>--уникальный ID операции платежей в майтакси (число)—обязательно (транзакция)\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>2</driver_id>\n<company_id>1</company_id> -- таксопарк в который переводятся средства\n<amount>1000</amount> -- сумма для списания\n<description>Причина списания</description>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Получение лимита корпоративного клиента", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>limit_get</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>111</client_id>\n<corp_id>40</corp_id> -- компания в которой необходимо запросить правило лимита, клиент может работать в нескольких компаниях сразу\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Обновление правила по лимиту", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>limit_update</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>40569</client_id>\n<corp_id>40</corp_id> -- компания в которой необходимо запросить правило лимита, клиент может работать в нескольких компаниях сразу\n<amount>1000</amount> -- сумма для списания\n<repeatability>once</repeatability> -- будет ли каждо-месячное обновление month, либо одноразово once\n<date_end>2018-08-22</date_end> -- если пустой элемент, означает что лимит бессрочный\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Создание лимита", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>limit_insert</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>43043</client_id>\n<corp_id>40</corp_id> -- компания в которой необходимо запросить правило лимита, клиент может работать в нескольких компаниях сразу\n<amount>10000</amount> -- сумма для списания\n<repeatability>month</repeatability> -- будет ли каждо-месячное обновление month, либо одноразово once\n<date_end>2023-08-22</date_end> -- если пустой элемент, означает что лимит бессрочный\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Удаление лимита", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>limit_delete</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>40569</client_id>\n<corp_id>40</corp_id> -- компания в которой необходимо запросить правило лимита, клиент может работать в нескольких компаниях сразу\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Получить список возможных счетов для операции", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_accounts_for_operation</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<operation>driver_deposit</operation>\n</ROOT>\n"}, "url": {"raw": "http://**************:88", "protocol": "http", "host": ["185", "100", "53", "100"], "port": "88"}}, "response": []}, {"name": "Получить информацию промокода", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_promocode_info</action>\n<client_id>46755</client_id>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<promocode>AAA11</promocode>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Проверить может ли клиент использовать промокод", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>validate_promocode</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>22685</client_id>\n<corp_id>40</corp_id>\n<promocode>ASDASD</promocode>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Получить отчет компаний", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_report_corp_short</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<skip>0</skip>\n<limit>10</limit>\n<date_to>2017-03-01</date_to>\n<date_from>2017-01-01</date_from>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Получить отчет таксопарков", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_ts_payments</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<skip>0</skip>\n<limit>10</limit>\n<date_to>2017-03-01</date_to>\n<date_from>2017-01-01</date_from>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Получить список перевода средств с водителя", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_driver_transfers</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>397</driver_id>\n<skip>0</skip>\n<limit>10</limit>\n<date_to>2017-08-01</date_to>\n<date_from>2017-01-01</date_from>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Доступость платежеспособности корпоративного сотрудника", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>ride_possibility</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>44841</client_id>\n<corp_client_id>40</corp_client_id>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Оценка для поездки", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>order_rating_from_client</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<ride_id>20179</ride_id>\n<rate>4</rate>\n</ROOT>\n"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "Оплата наличными", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<ROOT>\n  <action>client_pay_cash</action>\n  <username>{{username}}</username>\n  <password>{{password}}</password>\n  <mytaxi_id>25791346120083</mytaxi_id>\n  <ride_id>1114283288888814</ride_id>\n  <client_id>111</client_id>\n  <driver_id>111</driver_id>\n  <order_initiator>3</order_initiator>\n  <amount>100000</amount>\n  <point_a>Я<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Б<PERSON> Б<PERSON>бо<PERSON>в), дом 10</point_a>\n  <point_b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ур<PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>), дом 98</point_b>\n  <coordinates_a_x>41.292681</coordinates_a_x>\n  <coordinates_a_y>69.243231</coordinates_a_y>\n  <coordinates_b_x>41.292539</coordinates_b_x>\n  <coordinates_b_y>69.242383</coordinates_b_y>\n  <time_order_start>2019-01-29 10:53:45</time_order_start>\n  <time_driver_arrived>2019-01-29 10:53:55</time_driver_arrived>\n  <time_client_sat>2019-01-29 10:54:16</time_client_sat>\n  <time_order_finished>2019-01-29 10:54:29</time_order_finished>\n  <distance_to_client>0</distance_to_client>\n  <distance_before_client_sat>-151</distance_before_client_sat>\n  <order_distance>0</order_distance>\n  <government_num_car>111 AAA</government_num_car>\n  <model_car>Gentra</model_car>\n  <color_car>Молочная</color_car>\n  <tariff_name>Эконом</tariff_name>\n  <min_sum_by_tarif>5000</min_sum_by_tarif>\n  <cost_waiting_time>400</cost_waiting_time>\n  <cost_km>1200</cost_km>\n  <include_km_in_tarif>1</include_km_in_tarif>\n  <include_waiting_time>180</include_waiting_time>\n  <waiting_time_order>0</waiting_time_order>\n  <rate>20.00</rate>\n  <promocode></promocode>\n  <paid_from_bonus>0</paid_from_bonus>\n  <without_cashback>0</without_cashback>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": [{"name": "Оплата наличными", "originalRequest": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<ROOT>\n  <action>client_pay_cash</action>\n  <username>{{username}}</username>\n  <password>{{password}}</password>\n  <mytaxi_id>1017077777777</mytaxi_id>\n  <ride_id>4283288888880</ride_id>\n  <driver_id>9618</driver_id>\n  <order_initiator>2</order_initiator>\n  <amount>10000</amount>\n  <client_id>22848</client_id>\n  <point_a>Я<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ур<PERSON><PERSON><PERSON><PERSON><PERSON> (Б. Бобоев), дом 106 Ташкент</point_a>\n  <coordinates_a_x>41.292681</coordinates_a_x>\n  <coordinates_a_y>69.243231</coordinates_a_y>\n  <point_b>Я<PERSON>ка<PERSON>арай, Урикзор (Б. Бобоев), дом 98</point_b>\n  <coordinates_b_x>41.292539599706146</coordinates_b_x>\n  <coordinates_b_y>69.24238357447324</coordinates_b_y>\n  <time_order_start>2019-01-29 10:53:45</time_order_start>\n  <time_driver_arrived>2019-01-29 10:53:55</time_driver_arrived>\n  <time_client_sat>2019-01-29 10:54:16</time_client_sat>\n  <time_order_finished>2019-01-29 10:54:29</time_order_finished>\n  <distance_to_client>0</distance_to_client>\n  <distance_before_client_sat>-151</distance_before_client_sat>\n  <order_distance>0</order_distance>\n  <government_num_car>111 AAA</government_num_car>\n  <model_car>Gentra</model_car>\n  <color_car>Молочная</color_car>\n  <tariff_name>Эконом</tariff_name>\n  <min_sum_by_tarif>5000</min_sum_by_tarif>\n  <cost_waiting_time>400</cost_waiting_time>\n  <cost_km>1200</cost_km>\n  <include_km_in_tarif>1</include_km_in_tarif>\n  <include_waiting_time>180</include_waiting_time>\n  <rate>20.00</rate>\n  <waiting_time_order>0</waiting_time_order>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Date", "value": "Sat, 06 Jul 2024 08:25:56 GMT"}, {"key": "Server", "value": "Apache/2.2.22 (Win32) mod_ssl/2.2.22 OpenSSL/0.9.8t PHP/5.3.5"}, {"key": "X-Powered-By", "value": "PHP/5.3.5"}, {"key": "Content-Length", "value": "324"}, {"key": "Keep-Alive", "value": "timeout=5, max=100"}, {"key": "Connection", "value": "Keep-Alive"}, {"key": "Content-Type", "value": "application/xml", "description": "", "type": "text"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<ROOT>\n    <error>0</error>\n    <success>1</success>\n    <error_note></error_note>\n    <cashless_rides>0</cashless_rides>\n    <amount_promo>0</amount_promo>\n    <is_blocked>0</is_blocked>\n    <notification>0</notification>\n    <promo_error_code>100</promo_error_code>\n    <driver_transfer_amount>0</driver_transfer_amount>\n</ROOT>"}]}, {"name": "Оплата корпоративным клиентом", "request": {"method": "POST", "header": [{"key": "APP-LANGUAGE", "value": "{{lang}}"}, {"key": "APP-VERSION", "value": "12"}, {"key": "Content-Type", "value": "application/xml"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\"?>\n<ROOT>\n<action>corp_pay</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<mytaxi_id>18533</mytaxi_id>--уникальный ID операции платежей в майтакси (число)—обязательно (транзакция)\n<ride_id>27606</ride_id> -- id поездки -обязательно\n<driver_id>9707</driver_id> -- обязательно\n<amount>10000</amount> -- сумма поездки -обязательно\n<rate>15.00</rate> -- процентная ставка -обязательно\n<order_initiator>2</order_initiator>\n<client_id>43043</client_id>\n<corp_id>40</corp_id>\n<point_a>&#x42F;&#x43A;&#x43A;&#x430;&#x441;&#x430;&#x440;&#x430;&#x439;, &#x423;&#x440;&#x438;&#x43A;&#x437;&#x43E;&#x440; (&#x411;. &#x411;&#x43E;&#x431;&#x43E;&#x435;&#x432;), &#x434;&#x43E;&#x43C; 149 &#x422;&#x430;&#x448;&#x43A;&#x435;&#x43D;&#x442;</point_a>\n<coordinates_a_x>41.294</coordinates_a_x>\n<coordinates_a_y>69.24665</coordinates_a_y>\n<point_b>&#x42F;&#x43A;&#x43A;&#x430;&#x441;&#x430;&#x440;&#x430;&#x439;, &#x423;&#x440;&#x438;&#x43A;&#x437;&#x43E;&#x440; (&#x411;. &#x411;&#x43E;&#x431;&#x43E;&#x435;&#x432;), &#x434;&#x43E;&#x43C; 7&#x430;</point_b>\n<coordinates_b_x>41.29379179</coordinates_b_x>\n<coordinates_b_y>69.24632855</coordinates_b_y>\n<time_order_start>2017-08-21 11:38:44</time_order_start>\n<time_driver_arrived>2017-08-21 11:38:52</time_driver_arrived>\n<time_client_sat>2017-08-21 11:38:55</time_client_sat>\n<time_order_finished>2017-08-21 11:38:56</time_order_finished>\n<distance_to_client>0</distance_to_client>\n<distance_before_client_sat>0</distance_before_client_sat>\n<order_distance>0</order_distance>\n<government_num_car>01 111 SS</government_num_car>\n<model_car>Android 7</model_car>\n<color_car>&#x41A;&#x440;&#x430;&#x441;&#x43D;&#x44B;&#x439;</color_car>\n<tariff_name>&#x42D;&#x43A;&#x43E;&#x43D;&#x43E;&#x43C;</tariff_name>\n<min_sum_by_tarif>6000</min_sum_by_tarif>\n<cost_waiting_time>300</cost_waiting_time>\n<cost_km>900</cost_km>\n<include_km_in_tarif>4</include_km_in_tarif>\n<include_waiting_time>300</include_waiting_time>\n<waiting_time_order>0</waiting_time_order>\n</ROOT>"}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "corp pay", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>corp_deposit</action>\n<username>{{username}}</username>\n<password>{{password}}</password>\n<corp_id>711</corp_id>\n<amount>10000</amount>\n<cash>2</cash>\n<id_account>20210000400531230001</id_account>\n<description>Подтверждено</description>\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "get promocode info", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_promocode_info</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<promocode>NEWW5WLG7DBMXOD4GATZ</promocode>\n<client_id>48172</client_id>\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "get promocode validation", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>validate_promocode</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<client_id>111</client_id>\n<promocode>AAA11</promocode>\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "driver pay", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_deposit</action>\n<mytaxi_id>124578451511717187</mytaxi_id>--уникальный ID операции платежей в майтакси (число)—обязательно (транзакция)\n<cash>2</cash>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n<driver_id>111</driver_id>\n<amount>1000</amount> -- сумма для списания\n<comment>Причина списания</comment>\n<id_account>11556</id_account> -- счет в котором будет фиксироваться списанная сумма\n</ROOT>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}}, "response": []}, {"name": "all balance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>get_all_balance</action>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n</ROOT>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{url}}", "host": ["{{url}}"]}}, "response": []}, {"name": "move balance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ROOT>\n<action>driver_tax</action>\n<mytaxi_id>***********</mytaxi_id>\n<ride_id>********</ride_id>\n<driver_id>111</driver_id>\n<amount>20000</amount>\n<comment>С вас была снята сумма 20 000 сум для расчета налога</comment>\n<username>{{username}}</username>-- обязательно\n<password>{{password}}</password>-- обязательно\n</ROOT>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{staging}}", "host": ["{{staging}}"]}}, "response": []}], "auth": {"type": "basic", "basic": [{"key": "password", "value": "6dd21114327bf55c16af905fe0acff845acce3e5fb39f72aab25a40a31bb355c", "type": "string"}, {"key": "username", "value": "MytaxiAPIClick2015", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "url", "value": "http://*************:88", "type": "string"}, {"key": "username", "value": "MytaxiAPIClick2015", "type": "string"}, {"key": "password", "value": "6dd21114327bf55c16af905fe0acff845acce3e5fb39f72aab25a40a31bb355c", "type": "string"}, {"key": "count", "value": "1", "type": "string"}, {"key": "driver_id", "value": "111", "type": "string"}, {"key": "url", "value": "http://*************:88", "type": "string", "disabled": true}, {"key": "url", "value": "**************", "type": "string", "disabled": true}, {"key": "staging", "value": "http://**************:88", "type": "string"}, {"key": "staging", "value": "", "type": "string", "disabled": true}]}